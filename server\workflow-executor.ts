import { storage } from "./storage";
import { aiService } from "./ai-service";
import { WorkflowLogger, LoggingService, LogCategory } from "./logging-service";
import type { Workflow, WorkflowRun } from "@shared/schema";
import axios, { AxiosError } from 'axios';
import rateLimit from 'express-rate-limit';
import { validate, ValidationError } from 'jsonschema';
import { NodeExecutorUtils } from './node-executor-utils';
import { EnhancedCustomNodeExecutor } from './enhanced-custom-executor';

export interface NodeExecutor {
  execute(nodeData: any, input: any, context: ExecutionContext, nodeId?: string): Promise<any>;
}

export interface ExecutionContext {
  workflowRun: WorkflowRun;
  workflow: Workflow;
  nodeOutputs: Map<string, any>;
  credentials: Map<number, any>;
  logger: WorkflowLogger;
}

export class WorkflowExecutor {
  private nodeExecutors: Map<string, NodeExecutor>;

  constructor() {
    this.nodeExecutors = new Map();
    this.registerNodeExecutors();
  }

  private registerNodeExecutors() {
    this.nodeExecutors.set('input', new InputNodeExecutor());
    this.nodeExecutors.set('prompt', new PromptNodeExecutor());
    this.nodeExecutors.set('agent', new AgentNodeExecutor());
    this.nodeExecutors.set('api', new ApiNodeExecutor());
    this.nodeExecutors.set('api-trigger', new ApiTriggerNodeExecutor());
    this.nodeExecutors.set('custom', new EnhancedCustomNodeExecutor());
  }

  async executeWorkflow(workflowRunId: number): Promise<void> {
    const startTime = Date.now();
    let logger: WorkflowLogger | undefined;

    try {
      const workflowRun = await storage.getWorkflowRun(workflowRunId);
      if (!workflowRun) {
        throw new Error(`Workflow run ${workflowRunId} not found`);
      }

      const workflow = await storage.getWorkflow(workflowRun.workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${workflowRun.workflowId} not found`);
      }

      // Create logger
      logger = LoggingService.createWorkflowLogger(workflowRunId, workflow.id);

      await logger.info(`Starting workflow execution: ${workflow.name}`, {
        workflowId: workflow.id,
        workflowName: workflow.name,
        triggerType: workflowRun.triggerType,
        input: workflowRun.input
      }, LogCategory.EXECUTION);

      // Update status to running
      const workflowNodes = workflow.nodes as Record<string, any>;
      await storage.updateWorkflowRun(workflowRunId, {
        status: 'running',
        totalNodes: Object.keys(workflowNodes).length,
        completedNodes: 0,
        failedNodes: 0
      });

      // Create execution context
      const context: ExecutionContext = {
        workflowRun,
        workflow,
        nodeOutputs: new Map(),
        credentials: new Map(),
        logger
      };

      // Load credentials
      await this.loadCredentials(context);

      // Get execution order
      const executionOrder = this.getExecutionOrder(workflow);
      await logger.info(`Execution order determined`, {
        nodeCount: executionOrder.length,
        executionOrder
      }, LogCategory.EXECUTION);

      logger.startPerformanceTimer('workflow_execution');

      let completedNodes = 0;
      let failedNodes = 0;

      // Execute nodes in order
      for (const nodeId of executionOrder) {
        try {
          await this.executeNode(nodeId, context);
          completedNodes++;
          await storage.updateWorkflowRun(workflowRunId, { completedNodes });
        } catch (error) {
          failedNodes++;
          await storage.updateWorkflowRun(workflowRunId, { failedNodes });
          throw error; // Re-throw to stop execution
        }
      }

      await logger.endPerformanceTimer('workflow_execution', {
        completedNodes,
        failedNodes,
        totalNodes: executionOrder.length
      });

      // Mark workflow as completed
      const endTime = Date.now();
      const executionDuration = endTime - startTime;

      await storage.updateWorkflowRun(workflowRunId, {
        status: 'completed',
        endTime: new Date(),
        executionDuration,
        completedNodes,
        failedNodes
      });

      await logger.info(`Workflow execution completed successfully`, {
        executionDuration,
        completedNodes,
        failedNodes,
        totalNodes: executionOrder.length
      }, LogCategory.EXECUTION);

    } catch (error) {
      const endTime = Date.now();
      const executionDuration = endTime - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const stackTrace = error instanceof Error ? error.stack : undefined;

      if (logger) {
        await logger.error(`Workflow execution failed: ${errorMessage}`, error as Error, {
          executionDuration,
          failurePoint: 'workflow_execution'
        }, LogCategory.ERROR);
      }

      await storage.updateWorkflowRun(workflowRunId, {
        status: 'failed',
        endTime: new Date(),
        executionDuration,
        errorMessage,
        stackTrace
      });

      throw error;
    }
  }

  private async loadCredentials(context: ExecutionContext): Promise<void> {
    const credentials = await storage.getCredentials();
    for (const credential of credentials) {
      context.credentials.set(credential.id, credential);
    }
  }

  private getExecutionOrder(workflow: Workflow): string[] {
    const nodes = Object.keys(workflow.nodes as Record<string, any>);
    const edges = Object.values(workflow.edges as Record<string, any>);
    const visited = new Set<string>();
    const order: string[] = [];

    // Simple topological sort
    const visit = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      // Visit dependencies first
      const incomingEdges = edges.filter((edge: any) => edge.target === nodeId);
      for (const edge of incomingEdges) {
        visit((edge as any).source);
      }

      order.push(nodeId);
    };

    for (const nodeId of nodes) {
      visit(nodeId);
    }

    return order;
  }

  private async executeNode(nodeId: string, context: ExecutionContext): Promise<void> {
    const startTime = Date.now();
    const node = (context.workflow.nodes as Record<string, any>)[nodeId];
    if (!node) {
      throw new Error(`Node ${nodeId} not found in workflow`);
    }

    const nodeLogger = context.logger.createNodeLogger(0, nodeId, node.type); // Will update with actual nodeRunId

    await nodeLogger.info(`Starting node execution: ${nodeId}`, {
      nodeType: node.type,
      nodeName: node.data?.name || nodeId,
      nodeConfiguration: node.data
    }, LogCategory.EXECUTION);

    // Create node run
    const nodeInput = this.getNodeInput(nodeId, context);
    const nodeRun = await storage.createNodeRun({
      workflowRunId: context.workflowRun.id,
      nodeId,
      nodeType: node.type,
      nodeName: node.data?.name || nodeId,
      status: 'running',
      input: nodeInput,
      output: {}
    });

    // Update logger with actual node run ID
    const actualNodeLogger = context.logger.createNodeLogger(nodeRun.id, nodeId, node.type);

    try {
      await actualNodeLogger.debug(`Node input data`, {
        input: nodeInput,
        inputSize: JSON.stringify(nodeInput).length
      }, LogCategory.EXECUTION);

      // Get node executor
      const executor = this.nodeExecutors.get(node.type);
      if (!executor) {
        throw new Error(`No executor found for node type: ${node.type}`);
      }

      actualNodeLogger.startPerformanceTimer(`node_${nodeId}_execution`);

      // Execute node
      const output = await executor.execute(node.data, nodeInput, context, nodeId);

      await actualNodeLogger.endPerformanceTimer(`node_${nodeId}_execution`, {
        outputSize: JSON.stringify(output).length,
        nodeType: node.type
      });

      await actualNodeLogger.debug(`Node output data`, {
        output,
        outputSize: JSON.stringify(output).length
      }, LogCategory.EXECUTION);

      // Store output
      context.nodeOutputs.set(nodeId, output);

      // Calculate execution metrics
      const endTime = Date.now();
      const executionDuration = endTime - startTime;

      // Update node run
      await storage.updateNodeRun(nodeRun.id, {
        status: 'completed',
        output,
        endTime: new Date(),
        executionDuration
      });

      await actualNodeLogger.info(`Node execution completed successfully`, {
        executionDuration,
        outputKeys: Object.keys(output || {}),
        success: true
      }, LogCategory.EXECUTION);

    } catch (error) {
      const endTime = Date.now();
      const executionDuration = endTime - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const stackTrace = error instanceof Error ? error.stack : undefined;

      await actualNodeLogger.error(`Node execution failed: ${errorMessage}`, error as Error, {
        executionDuration,
        nodeType: node.type,
        nodeConfiguration: node.data,
        input: nodeInput
      }, LogCategory.ERROR);

      await storage.updateNodeRun(nodeRun.id, {
        status: 'failed',
        error: errorMessage,
        stackTrace,
        endTime: new Date(),
        executionDuration
      });

      throw error;
    }
  }

  private getNodeInput(nodeId: string, context: ExecutionContext): any {
    const edges = Object.values(context.workflow.edges as Record<string, any>);
    const incomingEdges = edges.filter((edge: any) => edge.target === nodeId);

    if (incomingEdges.length === 0) {
      // Input node - use workflow run input
      return context.workflowRun.input;
    }

    // Collect outputs from source nodes
    const input: any = {};
    for (const edge of incomingEdges) {
      const sourceOutput = context.nodeOutputs.get((edge as any).source);
      if (sourceOutput !== undefined) {
        Object.assign(input, sourceOutput);
      }
    }

    return input;
  }
}

// Node Executors
class InputNodeExecutor implements NodeExecutor {
  async execute(_nodeData: any, input: any, _context: ExecutionContext, _nodeId?: string): Promise<any> {
    // Input nodes just pass through the input data
    return input;
  }
}

class PromptNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext, _nodeId?: string): Promise<any> {
    // Get credential
    const credential = context.credentials.get(nodeData.credentialId);
    if (!credential) {
      throw new Error(`Credential ${nodeData.credentialId} not found`);
    }

    // Validate input if validation schema is provided
    if (nodeData.inputValidation?.enabled && nodeData.inputValidation?.schema) {
      this.validateInput(input, nodeData.inputValidation.schema);
    }

    // Process prompt template
    let prompt = nodeData.prompt;
    const missingVariables: string[] = [];

    prompt = NodeExecutorUtils.replaceTemplateVariables(prompt, input, (variable) => {
      missingVariables.push(variable);
      context.logger.debug(`Template variable not found: ${variable}`, {
        availableKeys: NodeExecutorUtils.getAvailableKeys(input),
        inputStructure: NodeExecutorUtils.getInputStructure(input)
      }, LogCategory.EXECUTION);
    });

    await context.logger.debug(`PromptNodeExecutor: Processed prompt`, {
      originalPrompt: nodeData.prompt,
      processedPrompt: prompt,
      inputKeys: Object.keys(input),
      missingVariables,
      outputFormat: nodeData.outputFormat,
      hasSchema: !!nodeData.schema
    }, LogCategory.EXECUTION);

    // Use the AI service for real AI generation
    const result = await aiService.generateResponse({
      provider: nodeData.provider,
      model: nodeData.model,
      prompt,
      maxTokens: nodeData.maxTokens,
      temperature: nodeData.temperature,
      outputFormat: nodeData.outputFormat,
      schema: nodeData.schema
    }, credential);

    // Log the response format and structure
    await context.logger.debug(`PromptNodeExecutor: Generated response`, {
      format: result.format,
      hasContent: !!result.content,
      contentType: typeof result.content,
      isStructured: result.format === 'json'
    }, LogCategory.EXECUTION);

    return result;
  }

  private validateInput(input: any, schema: Record<string, any>): void {
    const validation = validate(input, schema);
    if (!validation.valid) {
      throw new Error(`Prompt node input validation failed: ${validation.errors.map((e: ValidationError) => e.message).join(', ')}`);
    }
  }
}

class AgentNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext, _nodeId?: string): Promise<any> {
    // Get credential
    const credential = context.credentials.get(nodeData.credentialId);
    if (!credential) {
      throw new Error(`Credential ${nodeData.credentialId} not found`);
    }

    // Validate input if validation schema is provided
    if (nodeData.inputValidation?.enabled && nodeData.inputValidation?.schema) {
      this.validateInput(input, nodeData.inputValidation.schema);
    }

    // Create structured prompt from input data
    const prompt = this.createStructuredPrompt(input, nodeData, context);

    await context.logger.debug(`AgentNodeExecutor: Generated prompt`, {
      inputStructure: NodeExecutorUtils.getInputStructure(input),
      promptLength: prompt.length,
      systemPromptLength: nodeData.systemPrompt?.length || 0,
      outputFormat: nodeData.outputFormat,
      hasSchema: !!nodeData.schema
    }, LogCategory.EXECUTION);

    // Use the AI service for real AI generation
    const result = await aiService.generateResponse({
      provider: nodeData.provider,
      model: nodeData.model,
      prompt,
      systemPrompt: nodeData.systemPrompt,
      maxTokens: nodeData.maxTokens,
      temperature: nodeData.temperature,
      outputFormat: nodeData.outputFormat,
      schema: nodeData.schema
    }, credential);

    // Log the response format and structure
    await context.logger.debug(`AgentNodeExecutor: Generated response`, {
      format: result.format,
      hasContent: !!result.content,
      contentType: typeof result.content,
      isStructured: result.format === 'json'
    }, LogCategory.EXECUTION);

    return result;
  }

  private validateInput(input: any, schema: Record<string, any>): void {
    const validation = validate(input, schema);
    if (!validation.valid) {
      throw new Error(`Agent node input validation failed: ${validation.errors.map((e: ValidationError) => e.message).join(', ')}`);
    }
  }

  private createStructuredPrompt(input: any, nodeData: any, _context: ExecutionContext): string {
    // Check if this is webhook data (has method, headers, etc.)
    const isWebhookData = NodeExecutorUtils.isWebhookData(input);

    if (isWebhookData) {
      return this.createWebhookPrompt(input, nodeData);
    } else {
      return this.createGenericPrompt(input, nodeData);
    }
  }

  private createWebhookPrompt(input: any, nodeData: any): string {
    const sections: string[] = [];

    // Add webhook context
    sections.push("I received a webhook request with the following details:");

    // Use shared utility to create webhook data sections
    sections.push(...NodeExecutorUtils.createWebhookDataSection(input));

    // Add instruction based on configuration
    if (nodeData.promptTemplate) {
      // Use custom prompt template if provided
      sections.push(`\nInstructions: ${nodeData.promptTemplate}`);
    } else {
      // Default instruction
      sections.push("\nPlease process this webhook data according to your system prompt and provide an appropriate response.");
    }

    return sections.join('\n\n');
  }

  private createGenericPrompt(input: any, nodeData: any): string {
    const sections: string[] = [];

    sections.push("I received the following input data:");
    sections.push(JSON.stringify(input, null, 2));

    // Add instruction based on configuration
    if (nodeData.promptTemplate) {
      sections.push(`\nInstructions: ${nodeData.promptTemplate}`);
    } else {
      sections.push("\nPlease process this input data according to your system prompt and provide an appropriate response.");
    }

    return sections.join('\n\n');
  }
}

class ApiNodeExecutor implements NodeExecutor {
  private rateLimiters: Map<string, any> = new Map();

  private getRateLimiter(nodeId: string, requestsPerMinute: number) {
    if (!this.rateLimiters.has(nodeId)) {
      this.rateLimiters.set(nodeId, rateLimit({
        windowMs: 60 * 1000, // 1 minute
        max: requestsPerMinute,
        message: 'Rate limit exceeded'
      }));
    }
    return this.rateLimiters.get(nodeId);
  }

  private validateRequest(input: any, schema: Record<string, any>): void {
    const validation = validate(input, schema);
    if (!validation.valid) {
      throw new Error(`Request validation failed: ${validation.errors.map((e: ValidationError) => e.message).join(', ')}`);
    }
  }

  private transformInputData(input: any, transformation: any, context: ExecutionContext): any {
    let result = input;

    // Apply input mapping if configured
    if (transformation.inputMapping) {
      result = {};
      for (const [targetKey, sourceKey] of Object.entries(transformation.inputMapping)) {
        const value = NodeExecutorUtils.getNestedValue(input, sourceKey as string);
        if (value !== undefined) {
          result[targetKey] = value;
        }
      }
    }

    // Apply custom transformation script if configured
    if (transformation.transformScript) {
      try {
        const transformFunction = new Function('input', 'context', `
          ${transformation.transformScript}
          return typeof transform === 'function' ? transform(input, context) : input;
        `);
        result = transformFunction(result, {
          workflowId: context.workflow.id,
          runId: context.workflowRun.id,
          nodeOutputs: Object.fromEntries(context.nodeOutputs)
        });
      } catch (error) {
        throw new Error(`Input transformation script failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return result;
  }

  private transformOutputData(output: any, transformation: any, _context: ExecutionContext): any {
    let result = output;

    // Apply output mapping if configured
    if (transformation.outputMapping) {
      const mappedData: any = {};
      for (const [targetKey, sourceKey] of Object.entries(transformation.outputMapping)) {
        const value = NodeExecutorUtils.getNestedValue(output, sourceKey as string);
        if (value !== undefined) {
          mappedData[targetKey] = value;
        }
      }
      result = { ...output, ...mappedData };
    }

    return result;
  }

  private processUrlWithPathParams(url: string, pathParams: Record<string, string>, input: any): string {
    let processedUrl = url;

    // Replace path parameters in URL
    for (const [paramName, paramTemplate] of Object.entries(pathParams)) {
      const paramValue = NodeExecutorUtils.replaceTemplateVariables(paramTemplate, input);
      // Replace both {{paramName}} and :paramName patterns
      processedUrl = processedUrl.replace(new RegExp(`\\{\\{${paramName}\\}\\}`, 'g'), paramValue);
      processedUrl = processedUrl.replace(new RegExp(`:${paramName}`, 'g'), paramValue);
    }

    // Also apply general template variable replacement
    processedUrl = NodeExecutorUtils.replaceTemplateVariables(processedUrl, input);

    return processedUrl;
  }

  private processHeaders(headers: Record<string, string>, input: any): Record<string, string> {
    const processedHeaders: Record<string, string> = {};

    for (const [key, value] of Object.entries(headers)) {
      processedHeaders[key] = NodeExecutorUtils.replaceTemplateVariables(value, input);
    }

    return processedHeaders;
  }

  private processQueryParams(queryParams: Record<string, string>, input: any): Record<string, string> {
    const processedParams: Record<string, string> = {};

    for (const [key, value] of Object.entries(queryParams)) {
      const processedValue = NodeExecutorUtils.replaceTemplateVariables(value, input);
      if (processedValue && processedValue !== `[${value}: not found]`) {
        processedParams[key] = processedValue;
      }
    }

    return processedParams;
  }

  async execute(nodeData: any, input: any, context: ExecutionContext, _nodeId?: string): Promise<any> {
    try {
      // Apply rate limiting if enabled
      if (nodeData.rateLimit?.enabled) {
        const limiter = this.getRateLimiter(context.workflowRun.id.toString(), nodeData.rateLimit.requestsPerMinute);
        await new Promise((resolve, reject) => {
          limiter({}, {}, (err: Error | null) => {
            if (err) reject(err);
            else resolve(undefined);
          });
        });
      }

      // Validate request if enabled
      if (nodeData.requestValidation?.enabled) {
        this.validateRequest(input, nodeData.requestValidation.schema);
      }

      // Process input data transformation if enabled
      let processedInput = input;
      if (nodeData.dataTransformation?.enabled) {
        processedInput = this.transformInputData(input, nodeData.dataTransformation, context);
      }

      // Build dynamic URL with path parameters
      let processedUrl = nodeData.url;
      if (nodeData.dynamicParams?.enabled && nodeData.dynamicParams.pathParams) {
        processedUrl = this.processUrlWithPathParams(nodeData.url, nodeData.dynamicParams.pathParams, processedInput);
      } else {
        // Apply template variables to URL even if dynamic params not explicitly enabled
        processedUrl = NodeExecutorUtils.replaceTemplateVariables(nodeData.url, processedInput);
      }

      // Configure axios with timeout if enabled
      const axiosConfig: any = {
        method: nodeData.method || 'POST',
        url: processedUrl,
        headers: this.processHeaders(nodeData.headers || {}, processedInput),
      };

      if (nodeData.timeout?.enabled) {
        axiosConfig.timeout = nodeData.timeout.milliseconds;
      }

      // Add query parameters for GET requests or if explicitly configured
      if (nodeData.method === 'GET' || (nodeData.dynamicParams?.enabled && nodeData.dynamicParams.queryParams)) {
        axiosConfig.params = this.processQueryParams(nodeData.dynamicParams?.queryParams || {}, processedInput);
      }

      // Add request body for non-GET requests
      if (nodeData.method !== 'GET') {
        if (nodeData.dynamicParams?.enabled && nodeData.dynamicParams.requestBody) {
          // Use configured request body template
          const bodyTemplate = nodeData.dynamicParams.requestBody;
          const processedBody = NodeExecutorUtils.replaceTemplateVariables(bodyTemplate, processedInput);
          try {
            axiosConfig.data = JSON.parse(processedBody);
          } catch (e) {
            // If not valid JSON, send as string
            axiosConfig.data = processedBody;
          }
        } else {
          // Use input data directly
          axiosConfig.data = processedInput;
        }
      }

      // Add authentication if configured
      if (nodeData.authType === 'apiKey' && nodeData.credentialId) {
        const credential = context.credentials.get(nodeData.credentialId);
        if (credential) {
          const headerName = nodeData.apiKeyHeader || 'x-api-key';
          axiosConfig.headers[headerName] = credential.apiKey;
        }
      }

      // Make the HTTP request
      const response = await axios(axiosConfig);

      // Format response based on configuration
      let formattedResponse = response.data;
      if (nodeData.responseFormat?.type === 'text') {
        formattedResponse = typeof response.data === 'string' ? response.data : JSON.stringify(response.data);
      } else if (nodeData.responseFormat?.type === 'binary') {
        formattedResponse = response.data;
      }

      // Validate response schema if configured
      if (nodeData.responseFormat?.schema) {
        this.validateRequest(formattedResponse, nodeData.responseFormat.schema);
      }

      // Apply output transformation if enabled
      let finalOutput = {
        status: 'success',
        statusCode: response.status,
        headers: response.headers,
        data: formattedResponse
      };

      if (nodeData.dataTransformation?.enabled) {
        finalOutput = this.transformOutputData(finalOutput, nodeData.dataTransformation, context);
      }

      return finalOutput;
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        throw new Error(`API request failed: ${axiosError.message}`);
      }
      throw error;
    }
  }
}

class ApiTriggerNodeExecutor implements NodeExecutor {
  private rateLimiters: Map<string, any> = new Map();

  private getRateLimiter(nodeId: string, requestsPerMinute: number) {
    if (!this.rateLimiters.has(nodeId)) {
      this.rateLimiters.set(nodeId, rateLimit({
        windowMs: 60 * 1000, // 1 minute
        max: requestsPerMinute,
        message: 'Rate limit exceeded'
      }));
    }
    return this.rateLimiters.get(nodeId);
  }

  private validateRequest(input: any, schema: Record<string, any>): void {
    const validation = validate(input, schema);
    if (!validation.valid) {
      throw new Error(`Request validation failed: ${validation.errors.map((e: ValidationError) => e.message).join(', ')}`);
    }
  }

  private getDataToValidate(input: any): any {
    const method = input.method?.toUpperCase();

    switch (method) {
      case 'GET':
        // For GET requests, validate query parameters
        return input.query || {};

      case 'POST':
      case 'PUT':
      case 'PATCH':
        // For POST/PUT/PATCH requests, validate request body
        // Fall back to query params if no body is present
        return input.body || input.query || {};

      case 'DELETE':
        // For DELETE requests, typically validate query params or path params
        // Some DELETE requests may have a body, so check both
        return input.body || input.query || {};

      default:
        // For unknown methods, validate the entire input structure
        return {
          method: input.method,
          headers: input.headers,
          query: input.query,
          body: input.body,
          params: input.params
        };
    }
  }

  private getValidationTarget(method: string): string {
    const upperMethod = method?.toUpperCase();

    switch (upperMethod) {
      case 'GET':
        return 'query_parameters';
      case 'POST':
      case 'PUT':
      case 'PATCH':
        return 'request_body';
      case 'DELETE':
        return 'request_body_or_query_parameters';
      default:
        return 'entire_request';
    }
  }

  async execute(nodeData: any, input: any, context: ExecutionContext, nodeId?: string): Promise<any> {
    try {
      // API trigger nodes receive webhook data and pass it through
      // The input should contain the webhook request data: method, headers, query, body, params

      // Validate the HTTP method if allowedMethods is configured
      if (nodeData.allowedMethods && nodeData.allowedMethods.length > 0) {
        const requestMethod = input.method?.toUpperCase();
        if (!nodeData.allowedMethods.map((m: string) => m.toUpperCase()).includes(requestMethod)) {
          throw new Error(`HTTP method ${requestMethod} not allowed. Allowed methods: ${nodeData.allowedMethods.join(', ')}`);
        }
      }

      // Apply rate limiting if enabled
      if (nodeData.rateLimit?.enabled) {
        const limiter = this.getRateLimiter(context.workflowRun.id.toString(), nodeData.rateLimit.requestsPerMinute);
        await new Promise((resolve, reject) => {
          limiter({}, {}, (err: Error | null) => {
            if (err) reject(err);
            else resolve(undefined);
          });
        });
      }

      // Validate request if enabled
      if (nodeData.requestValidation?.enabled) {
        // Validate data based on HTTP method conventions
        const dataToValidate = this.getDataToValidate(input);
        this.validateRequest(dataToValidate, nodeData.requestValidation.schema);
      }

      // Extract and structure the webhook data for downstream nodes
      const webhookData = {
        method: input.method,
        headers: input.headers,
        query: input.query,
        body: input.body,
        params: input.params,
        timestamp: new Date().toISOString(),
        triggerNodeId: nodeId || 'unknown',
        // Add validation metadata
        validation: {
          enabled: nodeData.requestValidation?.enabled || false,
          validatedData: nodeData.requestValidation?.enabled ? this.getDataToValidate(input) : null,
          validationTarget: this.getValidationTarget(input.method)
        }
      };

      return webhookData;
    } catch (error) {
      throw new Error(`API trigger node execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

class CustomNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext, _nodeId?: string): Promise<any> {
    try {
      // Get the custom code
      const code = nodeData.code || '';

      // Create a safe execution environment
      const customFunction = new Function('input', 'context', `
        ${code}

        // If the code doesn't define an execute function, create a default one
        if (typeof execute !== 'function') {
          function execute(input, context) {
            return { result: 'Custom node executed', data: input };
          }
        }

        return execute(input, context);
      `);

      // Execute the custom function
      const result = await customFunction(input, {
        workflowId: context.workflow.id,
        runId: context.workflowRun.id,
        nodeOutputs: Object.fromEntries(context.nodeOutputs)
      });

      return result;
    } catch (error) {
      throw new Error(`Custom node execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

export const workflowExecutor = new WorkflowExecutor();

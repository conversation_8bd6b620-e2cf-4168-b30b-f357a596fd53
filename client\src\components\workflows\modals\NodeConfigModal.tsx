import React, { useState } from 'react';
import { Node } from 'reactflow';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useQuery } from '@tanstack/react-query';
import { Credential, InputSchema } from '@/types/workflow';
import { Key, Info, Copy, Trash2, Plus } from 'lucide-react';
import CodeEditor from '@/components/ui/code-editor';
import EnhancedCodeEditor from '@/components/ui/enhanced-code-editor';
import JSSchemaBuilder from '@/components/workflows/schema-builder/JSSchemaBuilder';
import JSTestRunner from '@/components/workflows/testing/JSTestRunner';
import SchemaBuilder from '../SchemaBuilder';
import FormRenderer from '../FormRenderer';

interface NodeConfigModalProps {
  node: Node;
  onClose: () => void;
  onUpdate: (nodeId: string, data: any) => void;
}

// Variable Info Component
const VariableInfoPopover: React.FC = () => {
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedText(text);
    setTimeout(() => setCopiedText(null), 2000);
  };

  const variableExamples = [
    {
      category: "Basic Variables",
      description: "Access data from previous nodes using dot notation",
      examples: [
        { syntax: "{{variable_name}}", description: "Direct property access" },
        { syntax: "{{body.name}}", description: "Nested object property" },
        { syntax: "{{query.action}}", description: "Query parameter" },
        { syntax: "{{headers.user-agent}}", description: "Header value" }
      ]
    },
    {
      category: "Webhook Helpers",
      description: "Special functions for webhook data formatting",
      examples: [
        { syntax: "{{webhook.method}}", description: "HTTP method (GET, POST, etc.)" },
        { syntax: "{{webhook.body_json}}", description: "Pretty-printed JSON of request body" },
        { syntax: "{{webhook.query_params}}", description: "Pretty-printed JSON of query parameters" },
        { syntax: "{{webhook.headers_json}}", description: "Pretty-printed JSON of headers" },
        { syntax: "{{webhook.all_data}}", description: "Pretty-printed JSON of entire webhook data" }
      ]
    },
    {
      category: "JSON Helpers",
      description: "Format specific data sections as JSON",
      examples: [
        { syntax: "{{json.body}}", description: "JSON formatted request body" },
        { syntax: "{{json.query}}", description: "JSON formatted query parameters" },
        { syntax: "{{json.headers}}", description: "JSON formatted headers" },
        { syntax: "{{json.params}}", description: "JSON formatted URL parameters" }
      ]
    },
    {
      category: "Nested Access",
      description: "Access deeply nested properties",
      examples: [
        { syntax: "{{body.user.profile.name}}", description: "Deep nested property" },
        { syntax: "{{body.user.preferences.theme}}", description: "User preference setting" },
        { syntax: "{{body.metadata.source}}", description: "Metadata information" }
      ]
    }
  ];

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-neutral-500 hover:text-neutral-700">
          <Info className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 max-h-96 overflow-y-auto" align="start">
        <div className="space-y-4">
          <div className="space-y-1">
            <h4 className="font-medium text-sm">Template Variables Guide</h4>
            <p className="text-xs text-neutral-600 dark:text-neutral-400">
              Use double curly braces to insert dynamic data from previous nodes
            </p>
          </div>

          {variableExamples.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-2">
              <div>
                <h5 className="font-medium text-xs text-neutral-800 dark:text-neutral-200">
                  {category.category}
                </h5>
                <p className="text-xs text-neutral-600 dark:text-neutral-400">
                  {category.description}
                </p>
              </div>

              <div className="space-y-1">
                {category.examples.map((example, exampleIndex) => (
                  <div key={exampleIndex} className="flex items-center justify-between group">
                    <div className="flex-1 min-w-0">
                      <code className="text-xs font-mono bg-neutral-100 dark:bg-neutral-800 px-1.5 py-0.5 rounded">
                        {example.syntax}
                      </code>
                      <p className="text-xs text-neutral-600 dark:text-neutral-400 mt-0.5">
                        {example.description}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => copyToClipboard(example.syntax)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {copiedText && (
            <div className="text-xs text-green-600 dark:text-green-400 font-medium">
              Copied: {copiedText}
            </div>
          )}

          <div className="pt-2 border-t border-neutral-200 dark:border-neutral-700">
            <p className="text-xs text-neutral-600 dark:text-neutral-400">
              <strong>Note:</strong> Missing variables will be replaced with <code>[variable_name: not found]</code> and logged as warnings.
            </p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

const NodeConfigModal: React.FC<NodeConfigModalProps> = ({ node, onClose, onUpdate }) => {
  const { data: credentials } = useQuery<Credential[]>({
    queryKey: ['/api/credentials'],
  });

  const [formData, setFormData] = useState(() => {
    // Initialize form data based on node type and existing data
    switch(node.type) {
      case 'input':
        // Handle both new and legacy schema formats
        let inputSchema: InputSchema;
        if (node.data.schema && typeof node.data.schema === 'object' && 'fields' in node.data.schema) {
          // New schema format
          inputSchema = node.data.schema as InputSchema;
        } else {
          // Legacy schema format - convert to new format
          const legacySchema = node.data.schema || { "query": "string" };
          inputSchema = {
            version: "1.0",
            title: "Input Form",
            description: "Workflow input parameters",
            fields: Object.entries(legacySchema).map(([name, type], index) => ({
              id: `field_${index}`,
              name,
              label: name.charAt(0).toUpperCase() + name.slice(1),
              type: type as any,
              validation: { required: type === 'string' || type === 'number' }
            }))
          };
        }

        return {
          name: node.data.name || `Input Node`,
          description: node.data.description || 'Accepts input data for the workflow',
          schema: inputSchema,
          legacySchema: JSON.stringify(node.data.legacySchema || node.data.schema || { "query": "string" }, null, 2)
        };
      case 'prompt':
        return {
          name: node.data.name || `Prompt Node`,
          description: node.data.description || 'Processes text through an LLM prompt',
          prompt: node.data.prompt || '',
          provider: node.data.provider || 'google',
          model: node.data.model || 'Gemini 2.0 Flash',
          credentialId: node.data.credentialId || '',
          temperature: node.data.temperature || 0.7,
          maxTokens: node.data.maxTokens || 1000,
          outputFormat: node.data.outputFormat || 'text',
          schema: node.data.schema ? JSON.stringify(node.data.schema, null, 2) : '{}',
          inputValidation: {
            enabled: node.data.inputValidation?.enabled || false,
            schema: JSON.stringify(node.data.inputValidation?.schema || {}, null, 2)
          }
        };
      case 'agent':
        return {
          name: node.data.name || `Agent Node`,
          description: node.data.description || 'Uses an LLM with a system role',
          systemPrompt: node.data.systemPrompt || '',
          provider: node.data.provider || 'google',
          model: node.data.model || 'Gemini 2.0 Flash',
          credentialId: node.data.credentialId || '',
          temperature: node.data.temperature || 0.7,
          maxTokens: node.data.maxTokens || 1000,
          outputFormat: node.data.outputFormat || 'text',
          schema: JSON.stringify(node.data.schema || {}, null, 2),
          inputValidation: {
            enabled: node.data.inputValidation?.enabled || false,
            schema: JSON.stringify(node.data.inputValidation?.schema || {}, null, 2)
          },
          promptTemplate: node.data.promptTemplate || ''
        };
      case 'api':
        return {
          name: node.data.name || `API Node`,
          description: node.data.description || 'Makes an HTTP request to an API endpoint',
          url: node.data.url || 'https://api.example.com/endpoint',
          method: node.data.method || 'POST',
          headers: JSON.stringify(node.data.headers || { "Content-Type": "application/json" }, null, 2),
          authType: node.data.authType || 'none',
          apiKeyHeader: node.data.apiKeyHeader || 'x-api-key',
          credentialId: node.data.credentialId || '',
          dynamicParams: {
            enabled: node.data.dynamicParams?.enabled || false,
            queryParams: JSON.stringify(node.data.dynamicParams?.queryParams || {}, null, 2),
            requestBody: node.data.dynamicParams?.requestBody || '',
            pathParams: JSON.stringify(node.data.dynamicParams?.pathParams || {}, null, 2)
          },
          dataTransformation: {
            enabled: node.data.dataTransformation?.enabled || false,
            inputMapping: JSON.stringify(node.data.dataTransformation?.inputMapping || {}, null, 2),
            outputMapping: JSON.stringify(node.data.dataTransformation?.outputMapping || {}, null, 2),
            transformScript: node.data.dataTransformation?.transformScript || ''
          },
          rateLimit: {
            enabled: node.data.rateLimit?.enabled || false,
            requestsPerMinute: node.data.rateLimit?.requestsPerMinute || 60
          },
          timeout: {
            enabled: node.data.timeout?.enabled || false,
            milliseconds: node.data.timeout?.milliseconds || 30000
          },
          requestValidation: {
            enabled: node.data.requestValidation?.enabled || false,
            schema: JSON.stringify(node.data.requestValidation?.schema || {}, null, 2)
          },
          responseFormat: {
            type: node.data.responseFormat?.type || 'json',
            schema: JSON.stringify(node.data.responseFormat?.schema || {}, null, 2)
          }
        };
      case 'api-trigger':
        return {
          name: node.data.name || `API Trigger`,
          description: node.data.description || 'Receives webhook requests to trigger workflow',
          allowedMethods: node.data.allowedMethods || ['POST'],
          authType: node.data.authType || 'none',
          apiKeyHeader: node.data.apiKeyHeader || 'x-api-key',
          credentialId: node.data.credentialId || '',
          rateLimit: {
            enabled: node.data.rateLimit?.enabled || false,
            requestsPerMinute: node.data.rateLimit?.requestsPerMinute || 60
          },
          requestValidation: {
            enabled: node.data.requestValidation?.enabled || false,
            schema: JSON.stringify(node.data.requestValidation?.schema || {}, null, 2)
          },
          responseConfig: {
            successStatus: node.data.responseConfig?.successStatus || 200,
            successMessage: node.data.responseConfig?.successMessage || 'Workflow triggered successfully',
            errorStatus: node.data.responseConfig?.errorStatus || 500,
            errorMessage: node.data.responseConfig?.errorMessage || 'Failed to trigger workflow'
          }
        };
      case 'custom':
        return {
          name: node.data.name || `Custom Node`,
          description: node.data.description || 'Custom node with user-defined logic',
          code: node.data.code || '// Define your custom logic here\nasync function execute(input, context) {\n  // Log the input for debugging\n  context.utils.log("info", "Processing input", input);\n  \n  // Your custom logic here\n  const result = {\n    result: "Custom processing complete",\n    data: input,\n    timestamp: new Date().toISOString()\n  };\n  \n  return result;\n}',

          // Enhanced configuration
          executionSettings: {
            timeout: node.data.executionSettings?.timeout || 30000,
            retryAttempts: node.data.executionSettings?.retryAttempts || 0,
            memoryLimit: node.data.executionSettings?.memoryLimit || 128,
            enableLogging: node.data.executionSettings?.enableLogging ?? true,
            enableProfiling: node.data.executionSettings?.enableProfiling ?? false,
            cacheEnabled: node.data.executionSettings?.cacheEnabled ?? false,
            batchProcessing: node.data.executionSettings?.batchProcessing ?? false,
            executionMode: node.data.executionSettings?.executionMode || 'sync'
          },

          // Enhanced input/output schema
          inputSchema: node.data.inputSchema || [{ name: 'input', type: 'any', required: true, description: 'Input data from previous node' }],
          outputSchema: node.data.outputSchema || [{ name: 'result', type: 'object', description: 'Processed output data' }],

          // Testing
          testCases: node.data.testCases || [],

          // Environment
          environment: {
            variables: node.data.environment?.variables || {},
            secrets: node.data.environment?.secrets || [],
            allowedDomains: node.data.environment?.allowedDomains || [],
            enabledLibraries: node.data.environment?.enabledLibraries || ['crypto', 'date', 'json', 'string', 'array']
          },

          // Metadata
          version: node.data.version || '1.0.0',
          author: node.data.author || '',
          tags: node.data.tags || [],

          // Legacy support
          inputs: JSON.stringify(node.data.inputs || [{ name: 'input', type: 'any', required: true }], null, 2),
          outputs: JSON.stringify(node.data.outputs || [{ name: 'output', type: 'any' }], null, 2)
        };
      default:
        return {
          name: node.data.name || `Node`,
          description: node.data.description || ''
        };
    }
  });

  const [expandedMethod, setExpandedMethod] = useState<string | null>(null);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    let updatedData = { ...node.data };

    // Process the form data based on node type
    switch(node.type) {
      case 'input':
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description,
          schema: formData.schema,
          legacySchema: formData.legacySchema ? JSON.parse(formData.legacySchema) : undefined
        };
        break;

      case 'prompt':
        try {
          const inputValidation = formData.inputValidation || { enabled: false, schema: '{}' };
          const schema = formData.schema && typeof formData.schema === 'string'
            ? JSON.parse(formData.schema)
            : formData.schema || {};

          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            prompt: formData.prompt,
            provider: formData.provider,
            model: formData.model,
            credentialId: parseInt(formData.credentialId) || null,
            temperature: parseFloat(formData.temperature),
            maxTokens: parseInt(formData.maxTokens),
            outputFormat: formData.outputFormat,
            schema: schema,
            inputValidation: {
              enabled: inputValidation.enabled,
              schema: JSON.parse(inputValidation.schema || '{}')
            }
          };
        } catch (e) {
          alert('Invalid JSON format in configuration');
          return;
        }
        break;

      case 'agent':
        try {
          const inputValidation = formData.inputValidation || { enabled: false, schema: '{}' };

          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            systemPrompt: formData.systemPrompt,
            provider: formData.provider,
            model: formData.model,
            credentialId: parseInt(formData.credentialId) || null,
            temperature: parseFloat(formData.temperature),
            maxTokens: parseInt(formData.maxTokens),
            outputFormat: formData.outputFormat,
            schema: formData.schema && typeof formData.schema === 'string' ? JSON.parse(formData.schema) : formData.schema,
            inputValidation: {
              enabled: inputValidation.enabled,
              schema: JSON.parse(inputValidation.schema || '{}')
            },
            promptTemplate: formData.promptTemplate || ''
          };
        } catch (e) {
          alert('Invalid JSON schema format');
          return;
        }
        break;

      case 'api':
        try {
          const rateLimit = formData.rateLimit || { enabled: false, requestsPerMinute: 60 };
          const timeout = formData.timeout || { enabled: false, milliseconds: 30000 };
          const requestValidation = formData.requestValidation || { enabled: false, schema: '{}' };
          const responseFormat = formData.responseFormat || { type: 'json', schema: '{}' };
          const dynamicParams = formData.dynamicParams || { enabled: false, queryParams: '{}', requestBody: '', pathParams: '{}' };
          const dataTransformation = formData.dataTransformation || { enabled: false, inputMapping: '{}', outputMapping: '{}', transformScript: '' };

          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            url: formData.url,
            method: formData.method,
            headers: JSON.parse(formData.headers || '{}'),
            authType: formData.authType,
            apiKeyHeader: formData.apiKeyHeader,
            credentialId: formData.credentialId === 'none' ? null : parseInt(formData.credentialId),
            dynamicParams: {
              enabled: dynamicParams.enabled,
              queryParams: JSON.parse(dynamicParams.queryParams || '{}'),
              requestBody: dynamicParams.requestBody || '',
              pathParams: JSON.parse(dynamicParams.pathParams || '{}')
            },
            dataTransformation: {
              enabled: dataTransformation.enabled,
              inputMapping: JSON.parse(dataTransformation.inputMapping || '{}'),
              outputMapping: JSON.parse(dataTransformation.outputMapping || '{}'),
              transformScript: dataTransformation.transformScript || ''
            },
            rateLimit: {
              enabled: rateLimit.enabled,
              requestsPerMinute: parseInt(String(rateLimit.requestsPerMinute)) || 60
            },
            timeout: {
              enabled: timeout.enabled,
              milliseconds: parseInt(String(timeout.milliseconds)) || 30000
            },
            requestValidation: {
              enabled: requestValidation.enabled,
              schema: JSON.parse(requestValidation.schema || '{}')
            },
            responseFormat: {
              type: responseFormat.type || 'json',
              schema: JSON.parse(responseFormat.schema || '{}')
            }
          };
        } catch (e) {
          alert('Invalid JSON format in configuration');
          return;
        }
        break;

      case 'api-trigger':
        try {
          const rateLimit = formData.rateLimit || { enabled: false, requestsPerMinute: 60 };
          const requestValidation = formData.requestValidation || { enabled: false, schema: '{}' };
          const responseConfig = formData.responseConfig || {
            successStatus: 200,
            successMessage: 'Workflow triggered successfully',
            errorStatus: 500,
            errorMessage: 'Failed to trigger workflow'
          };

          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            allowedMethods: formData.allowedMethods || ['POST'],
            authType: formData.authType,
            apiKeyHeader: formData.apiKeyHeader,
            credentialId: formData.credentialId === 'none' ? null : parseInt(formData.credentialId),
            rateLimit: {
              enabled: rateLimit.enabled,
              requestsPerMinute: parseInt(String(rateLimit.requestsPerMinute)) || 60
            },
            requestValidation: {
              enabled: requestValidation.enabled,
              schema: JSON.parse(requestValidation.schema || '{}')
            },
            responseConfig: {
              successStatus: parseInt(String(responseConfig.successStatus)) || 200,
              successMessage: responseConfig.successMessage || 'Workflow triggered successfully',
              errorStatus: parseInt(String(responseConfig.errorStatus)) || 500,
              errorMessage: responseConfig.errorMessage || 'Failed to trigger workflow'
            }
          };
        } catch (e) {
          alert('Invalid JSON format in configuration');
          return;
        }
        break;

      case 'custom':
        try {
          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            code: formData.code,
            // Enhanced configuration
            executionSettings: {
              timeout: parseInt(String(formData.executionSettings?.timeout)) || 30000,
              retryAttempts: parseInt(String(formData.executionSettings?.retryAttempts)) || 0,
              memoryLimit: parseInt(String(formData.executionSettings?.memoryLimit)) || 128,
              enableLogging: formData.executionSettings?.enableLogging ?? true,
              enableProfiling: formData.executionSettings?.enableProfiling ?? false,
              cacheEnabled: formData.executionSettings?.cacheEnabled ?? false,
              batchProcessing: formData.executionSettings?.batchProcessing ?? false,
              executionMode: formData.executionSettings?.executionMode || 'sync'
            },
            // Input/Output schema
            inputSchema: formData.inputSchema || [],
            outputSchema: formData.outputSchema || [],
            // Testing
            testCases: formData.testCases || [],
            // Environment
            environment: {
              variables: formData.environment?.variables || {},
              secrets: formData.environment?.secrets || [],
              allowedDomains: formData.environment?.allowedDomains || [],
              enabledLibraries: formData.environment?.enabledLibraries || ['crypto', 'date', 'json', 'string', 'array']
            },
            // Metadata
            version: formData.version || '1.0.0',
            author: formData.author || '',
            tags: formData.tags || []
          };
        } catch (e) {
          alert('Invalid configuration format');
          return;
        }
        break;

      default:
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description
        };
    }

    onUpdate(node.id, updatedData);
    onClose();
  };

  const getTitle = () => {
    switch(node.type) {
      case 'input': return 'Configure Input Node';
      case 'prompt': return 'Configure Prompt Node';
      case 'agent': return 'Configure Agent Node';
      case 'api': return 'Configure API Node';
      case 'api-trigger': return 'Configure API Trigger Node';
      case 'custom': return 'Configure Custom Node';
      default: return 'Configure Node';
    }
  };

  const getDescription = () => {
    switch(node.type) {
      case 'input': return 'Configure the input parameters and schema for this workflow entry point.';
      case 'prompt': return 'Set up the prompt template and AI model settings for text processing.';
      case 'agent': return 'Configure the AI agent with system prompts and behavior settings.';
      case 'api': return 'Configure API endpoint settings, authentication, and request parameters.';
      case 'api-trigger': return 'Configure webhook settings, authentication, and validation for incoming HTTP requests.';
      case 'custom': return 'Define custom logic, input parameters, and output structure for this node.';
      default: return 'Configure the settings and parameters for this workflow node.';
    }
  };

  const getMethodExampleSchema = (method: string) => {
    switch (method) {
      case 'GET':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier" },
            limit: { type: "number", minimum: 1, maximum: 100, description: "Number of items to return" },
            offset: { type: "number", minimum: 0, description: "Number of items to skip" },
            filter: { type: "string", description: "Filter criteria" },
            sort: { type: "string", enum: ["asc", "desc"], description: "Sort order" }
          },
          required: ["id"]
        }, null, 2);

      case 'POST':
        return JSON.stringify({
          type: "object",
          properties: {
            name: { type: "string", minLength: 1, description: "Resource name" },
            email: { type: "string", format: "email", description: "Email address" },
            data: {
              type: "object",
              properties: {
                role: { type: "string", enum: ["admin", "user", "guest"] },
                preferences: { type: "object" }
              }
            }
          },
          required: ["name", "email"]
        }, null, 2);

      case 'PUT':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier" },
            name: { type: "string", minLength: 1, description: "Updated name" },
            email: { type: "string", format: "email", description: "Updated email" },
            status: { type: "string", enum: ["active", "inactive", "pending"] },
            data: { type: "object", description: "Complete resource data" }
          },
          required: ["id", "name", "email"]
        }, null, 2);

      case 'PATCH':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier" },
            name: { type: "string", minLength: 1, description: "Updated name (optional)" },
            email: { type: "string", format: "email", description: "Updated email (optional)" },
            status: { type: "string", enum: ["active", "inactive", "pending"] },
            metadata: { type: "object", description: "Partial updates to metadata" }
          },
          required: ["id"],
          additionalProperties: false
        }, null, 2);

      case 'DELETE':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier to delete" },
            force: { type: "boolean", description: "Force deletion even if resource has dependencies" },
            reason: { type: "string", description: "Reason for deletion (optional)" }
          },
          required: ["id"]
        }, null, 2);

      default:
        return JSON.stringify({
          type: "object",
          properties: {
            action: { type: "string", description: "Action to perform" },
            data: { type: "object", description: "Request data" }
          },
          required: ["action"]
        }, null, 2);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Common Fields - Always 2 columns */}
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="node-name">Node Name</Label>
              <Input
                id="node-name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                className="mt-1"
              />
            </div>

            <div className="col-span-2">
              <Label htmlFor="node-description">Description</Label>
              <Textarea
                id="node-description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                className="mt-1"
                rows={2}
              />
            </div>

            {/* Node Type Specific Fields */}
            {node.type === 'input' && (
              <div className="col-span-2">
                <Label>Input Schema Configuration</Label>
                <Tabs defaultValue="visual" className="mt-2">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="visual">Visual Builder</TabsTrigger>
                    <TabsTrigger value="preview">Form Preview</TabsTrigger>
                    <TabsTrigger value="legacy">Legacy JSON</TabsTrigger>
                  </TabsList>

                  <TabsContent value="visual" className="mt-4">
                    <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
                      <SchemaBuilder
                        schema={formData.schema as InputSchema}
                        onChange={(schema) => handleChange('schema', schema)}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="preview" className="mt-4">
                    <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
                      <FormRenderer
                        schema={formData.schema as InputSchema}
                        values={{}}
                        onChange={() => {}}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="legacy" className="mt-4">
                    <CodeEditor
                      value={formData.legacySchema || '{}'}
                      onChange={(value) => handleChange('legacySchema', value)}
                      language="json"
                      height="200px"
                    />
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                      Legacy JSON schema format (for backward compatibility)
                    </p>
                  </TabsContent>
                </Tabs>
              </div>
            )}

            {node.type === 'prompt' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-provider">LLM Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => handleChange('provider', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="google">Google</SelectItem>
                      <SelectItem value="openrouter">OpenRouter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-model">LLM Model</Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => handleChange('model', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Gemini 2.5 Pro Preview">Gemini 2.5 Pro Preview</SelectItem>
                      <SelectItem value="Gemini 2.5 Flash Preview">Gemini 2.5 Flash Preview</SelectItem>
                      <SelectItem value="Gemini 2.0 Flash">Gemini 2.0 Flash</SelectItem>
                      <SelectItem value="Gemini 2.0 Flash Lite">Gemini 2.0 Flash Lite</SelectItem>
                      <SelectItem value="Gemini 1.5 Pro">Gemini 1.5 Pro</SelectItem>
                      <SelectItem value="Gemini Pro">Gemini Pro (Legacy)</SelectItem>
                      <SelectItem value="Gemini Flash">Gemini Flash (Legacy)</SelectItem>
                      <SelectItem value="DeepSeek Chat V3">DeepSeek Chat V3</SelectItem>
                      <SelectItem value="DeepSeek R1">DeepSeek R1</SelectItem>
                      <SelectItem value="DeepSeek R1T Chimera">DeepSeek R1T Chimera</SelectItem>
                      <SelectItem value="Qwen3 235B">Qwen3 235B</SelectItem>
                      <SelectItem value="Llama 4 Maverick">Llama 4 Maverick</SelectItem>
                      <SelectItem value="MAI DS R1">MAI DS R1</SelectItem>
                      <SelectItem value="Qwen 2.5 72B">Qwen 2.5 72B</SelectItem>
                      <SelectItem value="Qwen 2.5 32B">Qwen 2.5 32B</SelectItem>
                      <SelectItem value="Qwen 2.5 14B">Qwen 2.5 14B</SelectItem>
                      <SelectItem value="Gemma 2 9B">Gemma 2 9B</SelectItem>
                      <SelectItem value="Gemma 2 27B">Gemma 2 27B</SelectItem>
                      <SelectItem value="DeepSeek R1 Distill">DeepSeek R1 Distill</SelectItem>
                      <SelectItem value="DeepSeek Chat">DeepSeek Chat</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-credential">API Credential</Label>
                  <Select
                    value={formData.credentialId.toString()}
                    onValueChange={(value) => handleChange('credentialId', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select credential" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {credentials?.map(cred => (
                        <SelectItem key={cred.id} value={cred.id.toString()}>
                          {cred.name} ({cred.provider})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label>Output Format</Label>
                  <RadioGroup
                    value={formData.outputFormat || 'text'}
                    onValueChange={(value) => handleChange('outputFormat', value)}
                    className="flex space-x-4 mt-1"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="text" id="text" />
                      <Label htmlFor="text">Text</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="json" id="json" />
                      <Label htmlFor="json">JSON</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="markdown" id="markdown" />
                      <Label htmlFor="markdown">Markdown</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="col-span-1">
                  <Label>Temperature</Label>
                  <Slider
                    value={[formData.temperature || 0.7]}
                    onValueChange={(value) => handleChange('temperature', value[0])}
                    min={0}
                    max={1}
                    step={0.1}
                    className="mt-1"
                  />
                  <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    {formData.temperature || 0.7}
                  </div>
                </div>

                <div className="col-span-1">
                  <Label>Max Tokens</Label>
                  <Input
                    type="number"
                    value={formData.maxTokens || 1000}
                    onChange={(e) => handleChange('maxTokens', parseInt(e.target.value))}
                    className="mt-1"
                  />
                </div>

                <div className="col-span-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="node-prompt">Prompt Template</Label>
                    <VariableInfoPopover />
                  </div>
                  <Textarea
                    id="node-prompt"
                    value={formData.prompt}
                    onChange={(e) => handleChange('prompt', e.target.value)}
                    className="mt-1 font-mono"
                    rows={8}
                    placeholder="Create a detailed outline for an article about: {{query}}"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Use double curly braces for variables: {`{{variable_name}}`}.
                    Webhook helpers: {`{{webhook.method}}`}, {`{{webhook.body_json}}`}, {`{{json.body}}`}
                  </p>
                </div>

                {formData.outputFormat === 'json' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-schema">Output Schema</Label>
                    <CodeEditor
                      value={typeof formData.schema === 'string' ? formData.schema : JSON.stringify(formData.schema || {}, null, 2)}
                      onChange={(value) => handleChange('schema', value)}
                      language="json"
                      height="200px"
                      className="mt-1"
                    />
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                      Define the expected structure of the JSON output. Example:
                      {`\n{
  "title": "string",
  "content": "string",
  "tags": ["string"]
}`}
                    </p>
                  </div>
                )}

                <div className="col-span-2">
                  <div className="flex items-center gap-2">
                    <Label>Input Validation</Label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.inputValidation?.enabled || false}
                        onChange={(e) => handleChange('inputValidation', {
                          ...formData.inputValidation,
                          enabled: e.target.checked
                        })}
                        className="rounded border-neutral-300 dark:border-neutral-700"
                      />
                      <Label>Enable</Label>
                    </div>
                  </div>
                  {formData.inputValidation?.enabled && (
                    <CodeEditor
                      value={typeof formData.inputValidation?.schema === 'string'
                        ? formData.inputValidation.schema
                        : JSON.stringify(formData.inputValidation?.schema || {}, null, 2)}
                      onChange={(value) => handleChange('inputValidation', {
                        ...formData.inputValidation,
                        schema: value
                      })}
                      language="json"
                      height="150px"
                      className="mt-1"
                    />
                  )}
                </div>
              </>
            )}

            {node.type === 'agent' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-provider">LLM Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => handleChange('provider', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="google">Google</SelectItem>
                      <SelectItem value="openrouter">OpenRouter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-model">LLM Model</Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => handleChange('model', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {formData.provider === 'google' ? (
                        <>
                          <SelectItem value="Gemini 2.5 Pro Preview">Gemini 2.5 Pro Preview</SelectItem>
                          <SelectItem value="Gemini 2.5 Flash Preview">Gemini 2.5 Flash Preview</SelectItem>
                          <SelectItem value="Gemini 2.0 Flash">Gemini 2.0 Flash</SelectItem>
                          <SelectItem value="Gemini 2.0 Flash Lite">Gemini 2.0 Flash Lite</SelectItem>
                          <SelectItem value="Gemini 1.5 Pro">Gemini 1.5 Pro</SelectItem>
                          <SelectItem value="Gemini Pro">Gemini Pro (Legacy)</SelectItem>
                          <SelectItem value="Gemini Flash">Gemini Flash (Legacy)</SelectItem>
                        </>
                      ) : (
                        <>
                          <SelectItem value="DeepSeek Chat V3">DeepSeek Chat V3</SelectItem>
                          <SelectItem value="DeepSeek R1">DeepSeek R1</SelectItem>
                          <SelectItem value="DeepSeek R1T Chimera">DeepSeek R1T Chimera</SelectItem>
                          <SelectItem value="Qwen3 235B">Qwen3 235B</SelectItem>
                          <SelectItem value="Llama 4 Maverick">Llama 4 Maverick</SelectItem>
                          <SelectItem value="MAI DS R1">MAI DS R1</SelectItem>
                          <SelectItem value="Qwen 2.5 72B">Qwen 2.5 72B</SelectItem>
                          <SelectItem value="Qwen 2.5 32B">Qwen 2.5 32B</SelectItem>
                          <SelectItem value="Qwen 2.5 14B">Qwen 2.5 14B</SelectItem>
                          <SelectItem value="Gemma 2 9B">Gemma 2 9B</SelectItem>
                          <SelectItem value="Gemma 2 27B">Gemma 2 27B</SelectItem>
                          <SelectItem value="DeepSeek R1 Distill">DeepSeek R1 Distill</SelectItem>
                          <SelectItem value="DeepSeek Chat">DeepSeek Chat</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-credential">API Credential</Label>
                  <Select
                    value={formData.credentialId.toString()}
                    onValueChange={(value) => handleChange('credentialId', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select credential" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {credentials?.map(cred => (
                        <SelectItem key={cred.id} value={cred.id.toString()}>
                          {cred.name} ({cred.provider})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label>Output Format</Label>
                  <RadioGroup
                    value={formData.outputFormat}
                    onValueChange={(value) => handleChange('outputFormat', value)}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="text" id="text-output" />
                      <Label htmlFor="text-output">Text</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="json" id="json-output" />
                      <Label htmlFor="json-output">JSON</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="col-span-1">
                  <Label>Temperature</Label>
                  <div className="flex items-center mt-2">
                    <Slider
                      min={0}
                      max={1}
                      step={0.1}
                      value={[parseFloat(formData.temperature)]}
                      onValueChange={([value]) => handleChange('temperature', value)}
                      className="w-full"
                    />
                    <span className="ml-2 text-sm">{formData.temperature}</span>
                  </div>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-max-tokens">Max Tokens</Label>
                  <Input
                    id="node-max-tokens"
                    type="number"
                    value={formData.maxTokens}
                    onChange={(e) => handleChange('maxTokens', e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div className="col-span-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="node-system-prompt">System Prompt</Label>
                    <VariableInfoPopover />
                  </div>
                  <Textarea
                    id="node-system-prompt"
                    value={formData.systemPrompt}
                    onChange={(e) => handleChange('systemPrompt', e.target.value)}
                    className="mt-1 font-mono"
                    rows={5}
                    placeholder="You are a professional content writer. Create a well-structured article based on this outline."
                  />
                </div>

                {formData.outputFormat === 'json' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-schema">JSON Schema (optional)</Label>
                    <CodeEditor
                      value={typeof formData.schema === 'string' ? formData.schema : '{}'}
                      onChange={(value) => handleChange('schema', value)}
                      language="json"
                      height="150px"
                      className="mt-1"
                    />
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                      Define the expected structure of the JSON output
                    </p>
                  </div>
                )}

                <div className="col-span-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="node-prompt-template">Custom Prompt Template (optional)</Label>
                    <VariableInfoPopover />
                  </div>
                  <Textarea
                    id="node-prompt-template"
                    value={formData.promptTemplate}
                    onChange={(e) => handleChange('promptTemplate', e.target.value)}
                    className="mt-1 font-mono"
                    rows={3}
                    placeholder="Analyze this webhook request for security implications and data quality..."
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Custom instructions for processing input data. If empty, uses default processing logic.
                  </p>
                </div>

                <div className="col-span-2">
                  <Label>Input Validation</Label>
                  <div className="mt-2 space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.inputValidation?.enabled || false}
                        onChange={(e) => handleChange('inputValidation', {
                          ...formData.inputValidation,
                          enabled: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Enable input validation</span>
                    </label>
                    {formData.inputValidation?.enabled && (
                      <div>
                        <Label htmlFor="agent-input-validation-schema">Input Schema</Label>
                        <CodeEditor
                          value={typeof formData.inputValidation?.schema === 'string'
                            ? formData.inputValidation.schema
                            : JSON.stringify(formData.inputValidation?.schema || {}, null, 2)}
                          onChange={(value) => handleChange('inputValidation', {
                            ...formData.inputValidation,
                            schema: value
                          })}
                          language="json"
                          height="150px"
                          className="mt-1"
                        />
                        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          Validate incoming data structure. Example: Require webhook data to have method and headers fields.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {node.type === 'api' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-url">API Endpoint</Label>
                  <Input
                    id="node-url"
                    value={formData.url}
                    onChange={(e) => handleChange('url', e.target.value)}
                    className="mt-1"
                    placeholder="https://api.example.com/endpoint"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-method">HTTP Method</Label>
                  <Select
                    value={formData.method}
                    onValueChange={(value) => handleChange('method', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GET">GET</SelectItem>
                      <SelectItem value="POST">POST</SelectItem>
                      <SelectItem value="PUT">PUT</SelectItem>
                      <SelectItem value="DELETE">DELETE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-headers">Headers (JSON)</Label>
                  <CodeEditor
                    value={formData.headers || '{}'}
                    onChange={(value) => handleChange('headers', value)}
                    language="json"
                    height="100px"
                    className="mt-1"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-auth-type">Authentication</Label>
                  <Select
                    value={formData.authType}
                    onValueChange={(value) => handleChange('authType', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select auth type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="apiKey">API Key</SelectItem>
                      <SelectItem value="bearer">Bearer Token</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.authType === 'apiKey' && (
                  <div className="col-span-1">
                    <Label htmlFor="node-api-key-header">API Key Header</Label>
                    <Input
                      id="node-api-key-header"
                      value={formData.apiKeyHeader}
                      onChange={(e) => handleChange('apiKeyHeader', e.target.value)}
                      className="mt-1"
                      placeholder="x-api-key"
                    />
                  </div>
                )}

                {formData.authType !== 'none' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-credential">Credential</Label>
                    <Select
                      value={formData.credentialId.toString()}
                      onValueChange={(value) => handleChange('credentialId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select credential" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {credentials?.map(cred => (
                          <SelectItem key={cred.id} value={cred.id.toString()}>
                            {cred.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Dynamic Parameters Section */}
                <div className="col-span-2">
                  <div className="flex items-center gap-2">
                    <Label>Dynamic Parameters</Label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.dynamicParams?.enabled || false}
                        onChange={(e) => handleChange('dynamicParams', {
                          ...formData.dynamicParams,
                          enabled: e.target.checked
                        })}
                        className="rounded border-neutral-300 dark:border-neutral-700"
                      />
                      <Label>Enable dynamic parameters</Label>
                    </div>
                  </div>
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Use template variables like {`{{variable_name}}`} to dynamically populate API parameters from input data.
                  </p>

                  {formData.dynamicParams?.enabled && (
                    <div className="mt-4 space-y-4 border rounded-lg p-4">
                      <div>
                        <Label>Query Parameters (JSON)</Label>
                        <CodeEditor
                          value={formData.dynamicParams?.queryParams || '{}'}
                          onChange={(value) => handleChange('dynamicParams', {
                            ...formData.dynamicParams,
                            queryParams: value
                          })}
                          language="json"
                          height="120px"
                          className="mt-1"
                        />
                        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          Example: {`{"limit": "{{limit}}", "filter": "{{status}}", "user_id": "{{user.id}}"}`}
                        </p>
                      </div>

                      <div>
                        <Label>Path Parameters (JSON)</Label>
                        <CodeEditor
                          value={formData.dynamicParams?.pathParams || '{}'}
                          onChange={(value) => handleChange('dynamicParams', {
                            ...formData.dynamicParams,
                            pathParams: value
                          })}
                          language="json"
                          height="120px"
                          className="mt-1"
                        />
                        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          For URLs like /users/{`{{user_id}}`}/posts/{`{{post_id}}`}. Example: {`{"user_id": "{{user.id}}", "post_id": "{{post.id}}"}`}
                        </p>
                      </div>

                      {formData.method !== 'GET' && (
                        <div>
                          <Label>Request Body Template</Label>
                          <CodeEditor
                            value={formData.dynamicParams?.requestBody || ''}
                            onChange={(value) => handleChange('dynamicParams', {
                              ...formData.dynamicParams,
                              requestBody: value
                            })}
                            language="json"
                            height="150px"
                            className="mt-1"
                          />
                          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                            JSON template for request body. Example: {`{"name": "{{user.name}}", "email": "{{user.email}}", "data": {{json.user_data}}}`}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Data Transformation Section */}
                <div className="col-span-2">
                  <div className="flex items-center gap-2">
                    <Label>Data Transformation</Label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.dataTransformation?.enabled || false}
                        onChange={(e) => handleChange('dataTransformation', {
                          ...formData.dataTransformation,
                          enabled: e.target.checked
                        })}
                        className="rounded border-neutral-300 dark:border-neutral-700"
                      />
                      <Label>Enable data transformation</Label>
                    </div>
                  </div>
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Transform input data before sending to API and transform API response before passing to next node.
                  </p>

                  {formData.dataTransformation?.enabled && (
                    <div className="mt-4 space-y-4 border rounded-lg p-4">
                      <div>
                        <Label>Input Mapping (JSON)</Label>
                        <CodeEditor
                          value={formData.dataTransformation?.inputMapping || '{}'}
                          onChange={(value) => handleChange('dataTransformation', {
                            ...formData.dataTransformation,
                            inputMapping: value
                          })}
                          language="json"
                          height="120px"
                          className="mt-1"
                        />
                        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          Map input fields to API parameters. Example: {`{"api_user_id": "user.id", "api_name": "user.full_name"}`}
                        </p>
                      </div>

                      <div>
                        <Label>Output Mapping (JSON)</Label>
                        <CodeEditor
                          value={formData.dataTransformation?.outputMapping || '{}'}
                          onChange={(value) => handleChange('dataTransformation', {
                            ...formData.dataTransformation,
                            outputMapping: value
                          })}
                          language="json"
                          height="120px"
                          className="mt-1"
                        />
                        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          Map API response fields to output. Example: {`{"user_id": "data.id", "user_name": "data.name"}`}
                        </p>
                      </div>

                      <div>
                        <Label>Custom Transform Script (JavaScript)</Label>
                        <CodeEditor
                          value={formData.dataTransformation?.transformScript || ''}
                          onChange={(value) => handleChange('dataTransformation', {
                            ...formData.dataTransformation,
                            transformScript: value
                          })}
                          language="javascript"
                          height="150px"
                          className="mt-1"
                        />
                        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          Custom transformation function. Example: {`function transform(input, context) { return { ...input, timestamp: Date.now() }; }`}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}

            {node.type === 'api-trigger' && (
              <>
                <div className="col-span-2">
                  <Label>Allowed HTTP Methods</Label>
                  <div className="mt-2 space-y-2">
                    {['GET', 'POST', 'PUT', 'DELETE'].map((method) => (
                      <label key={method} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.allowedMethods?.includes(method) || false}
                          onChange={(e) => {
                            const currentMethods = formData.allowedMethods || [];
                            if (e.target.checked) {
                              handleChange('allowedMethods', [...currentMethods, method]);
                            } else {
                              handleChange('allowedMethods', currentMethods.filter((m: string) => m !== method));
                            }
                          }}
                          className="rounded"
                        />
                        <span className="text-sm">{method}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-auth-type">Authentication</Label>
                  <Select
                    value={formData.authType}
                    onValueChange={(value) => handleChange('authType', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select auth type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="apiKey">API Key</SelectItem>
                      <SelectItem value="bearer">Bearer Token</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.authType === 'apiKey' && (
                  <div className="col-span-1">
                    <Label htmlFor="node-api-key-header">API Key Header</Label>
                    <Input
                      id="node-api-key-header"
                      value={formData.apiKeyHeader}
                      onChange={(e) => handleChange('apiKeyHeader', e.target.value)}
                      className="mt-1"
                      placeholder="x-api-key"
                    />
                  </div>
                )}

                {formData.authType !== 'none' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-credential">Credential</Label>
                    <Select
                      value={formData.credentialId.toString()}
                      onValueChange={(value) => handleChange('credentialId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select credential" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {credentials?.map(cred => (
                          <SelectItem key={cred.id} value={cred.id.toString()}>
                            {cred.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="col-span-2">
                  <Label>Rate Limiting</Label>
                  <div className="mt-2 space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.rateLimit?.enabled || false}
                        onChange={(e) => handleChange('rateLimit', {
                          ...formData.rateLimit,
                          enabled: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Enable rate limiting</span>
                    </label>
                    {formData.rateLimit?.enabled && (
                      <div>
                        <Label htmlFor="rate-limit-requests">Requests per minute</Label>
                        <Input
                          id="rate-limit-requests"
                          type="number"
                          value={formData.rateLimit?.requestsPerMinute || 60}
                          onChange={(e) => handleChange('rateLimit', {
                            ...formData.rateLimit,
                            requestsPerMinute: parseInt(e.target.value)
                          })}
                          className="mt-1"
                          min="1"
                          max="1000"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="col-span-2">
                  <Label>Request Validation</Label>
                  <div className="mt-2 space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.requestValidation?.enabled || false}
                        onChange={(e) => handleChange('requestValidation', {
                          ...formData.requestValidation,
                          enabled: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Enable request validation</span>
                    </label>
                    {formData.requestValidation?.enabled && (
                      <div>
                        <Label htmlFor="validation-schema">JSON Schema</Label>
                        <CodeEditor
                          value={formData.requestValidation?.schema || '{}'}
                          onChange={(value) => handleChange('requestValidation', {
                            ...formData.requestValidation,
                            schema: value
                          })}
                          language="json"
                          height="150px"
                          className="mt-1"
                        />
                        <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          <p className="mb-2">Define JSON schema to validate incoming request data.</p>

                          <div className="space-y-2">
                            {/* Method Examples */}
                            {['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].map((method) => (
                              <div key={method} className="border border-neutral-200 dark:border-neutral-700 rounded">
                                <button
                                  type="button"
                                  className={`w-full px-3 py-2 text-left text-xs font-medium flex items-center justify-between ${
                                    formData.allowedMethods?.includes(method)
                                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                                      : 'bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400'
                                  }`}
                                  onClick={() => setExpandedMethod(expandedMethod === method ? null : method)}
                                >
                                  <span>{method} - {method === 'GET' ? 'Query Parameters' : 'Request Body'}</span>
                                  <span className={`transform transition-transform ${expandedMethod === method ? 'rotate-180' : ''}`}>▼</span>
                                </button>
                                {(expandedMethod === method || formData.allowedMethods?.includes(method)) && (
                                  <div className="p-3 border-t border-neutral-200 dark:border-neutral-700">
                                    <p className="text-xs mb-2">Validates {method === 'GET' ? 'query parameters' : 'request body'} for {method.toLowerCase()} requests:</p>
                                    <pre className="text-xs bg-neutral-100 dark:bg-neutral-900 p-2 rounded overflow-x-auto">
                                      {getMethodExampleSchema(method)}
                                    </pre>
                                    <p className="text-xs mt-1 text-neutral-500">
                                      {method === 'GET' ? `Example: ?id=user123&limit=10&filter=active` : 'Fallback: Query parameters if no body present'}
                                    </p>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {node.type === 'custom' && (
              <div className="col-span-2">
                <Label>Enhanced Custom JavaScript Node Configuration</Label>
                <Tabs defaultValue="code" className="mt-2">
                  <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="code">Code</TabsTrigger>
                    <TabsTrigger value="schema">Input/Output</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                    <TabsTrigger value="testing">Testing</TabsTrigger>
                    <TabsTrigger value="environment">Environment</TabsTrigger>
                  </TabsList>

                  <TabsContent value="code" className="mt-4">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="node-code">JavaScript Code</Label>
                        <div className="mt-1">
                          <EnhancedCodeEditor
                            value={formData.code || ''}
                            onChange={(value) => handleChange('code', value)}
                            language="javascript"
                            height="400px"
                            templates={[
                              {
                                id: 'basic',
                                name: 'Basic Template',
                                description: 'Basic execute function',
                                category: 'utility',
                                code: 'async function execute(input, context) {\n  context.utils.log("info", "Processing input", input);\n  \n  return {\n    result: "success",\n    data: input\n  };\n}',
                                inputSchema: [],
                                outputSchema: []
                              },
                              {
                                id: 'http',
                                name: 'HTTP Request',
                                description: 'Make HTTP requests',
                                category: 'api-call',
                                code: 'async function execute(input, context) {\n  const response = await context.utils.fetch(input.url, {\n    method: input.method || "GET",\n    headers: input.headers || {},\n    body: input.body ? JSON.stringify(input.body) : undefined\n  });\n  \n  return await response.json();\n}',
                                inputSchema: [],
                                outputSchema: []
                              }
                            ]}
                          />
                        </div>
                        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          Write your custom JavaScript logic. Use async/await for asynchronous operations.
                          Access utilities via context.utils (fetch, log, crypto, date, json, string, array).
                        </p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="schema" className="mt-4">
                    <JSSchemaBuilder
                      inputSchema={formData.inputSchema || []}
                      outputSchema={formData.outputSchema || []}
                      onInputSchemaChange={(schema) => handleChange('inputSchema', schema)}
                      onOutputSchemaChange={(schema) => handleChange('outputSchema', schema)}
                      mode="both"
                    />
                  </TabsContent>

                  <TabsContent value="settings" className="mt-4">
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-medium mb-3">Execution Settings</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label>Timeout (milliseconds)</Label>
                            <Input
                              type="number"
                              value={formData.executionSettings?.timeout || 30000}
                              onChange={(e) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                timeout: parseInt(e.target.value) || 30000
                              })}
                              min="1000"
                              max="300000"
                            />
                          </div>

                          <div>
                            <Label>Retry Attempts</Label>
                            <Input
                              type="number"
                              value={formData.executionSettings?.retryAttempts || 0}
                              onChange={(e) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                retryAttempts: parseInt(e.target.value) || 0
                              })}
                              min="0"
                              max="5"
                            />
                          </div>

                          <div>
                            <Label>Memory Limit (MB)</Label>
                            <Input
                              type="number"
                              value={formData.executionSettings?.memoryLimit || 128}
                              onChange={(e) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                memoryLimit: parseInt(e.target.value) || 128
                              })}
                              min="64"
                              max="1024"
                            />
                          </div>

                          <div>
                            <Label>Execution Mode</Label>
                            <Select
                              value={formData.executionSettings?.executionMode || 'sync'}
                              onValueChange={(value) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                executionMode: value
                              })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="sync">Synchronous</SelectItem>
                                <SelectItem value="async">Asynchronous</SelectItem>
                                <SelectItem value="streaming">Streaming</SelectItem>
                                <SelectItem value="batch">Batch Processing</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="mt-4 space-y-3">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={formData.executionSettings?.enableLogging ?? true}
                              onCheckedChange={(checked) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                enableLogging: checked
                              })}
                            />
                            <Label>Enable Logging</Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={formData.executionSettings?.enableProfiling ?? false}
                              onCheckedChange={(checked) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                enableProfiling: checked
                              })}
                            />
                            <Label>Enable Performance Profiling</Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={formData.executionSettings?.cacheEnabled ?? false}
                              onCheckedChange={(checked) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                cacheEnabled: checked
                              })}
                            />
                            <Label>Enable Result Caching</Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={formData.executionSettings?.batchProcessing ?? false}
                              onCheckedChange={(checked) => handleChange('executionSettings', {
                                ...formData.executionSettings,
                                batchProcessing: checked
                              })}
                            />
                            <Label>Enable Batch Processing</Label>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Metadata</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label>Version</Label>
                            <Input
                              value={formData.version || '1.0.0'}
                              onChange={(e) => handleChange('version', e.target.value)}
                              placeholder="1.0.0"
                            />
                          </div>

                          <div>
                            <Label>Author</Label>
                            <Input
                              value={formData.author || ''}
                              onChange={(e) => handleChange('author', e.target.value)}
                              placeholder="Your name"
                            />
                          </div>

                          <div className="col-span-2">
                            <Label>Tags (comma-separated)</Label>
                            <Input
                              value={formData.tags?.join(', ') || ''}
                              onChange={(e) => handleChange('tags', e.target.value.split(',').map(t => t.trim()).filter(Boolean))}
                              placeholder="data-processing, api, utility"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="testing" className="mt-4">
                    <JSTestRunner
                      code={formData.code || ''}
                      inputSchema={formData.inputSchema || []}
                      outputSchema={formData.outputSchema || []}
                      testCases={formData.testCases || []}
                      onTestCasesChange={(testCases) => handleChange('testCases', testCases)}
                      onRunTest={async (testCase) => {
                        // Mock test execution for now
                        return {
                          testId: testCase.id,
                          success: true,
                          output: { result: 'Mock test result', data: testCase.input },
                          executionTime: Math.floor(Math.random() * 100) + 10
                        };
                      }}
                      onRunAllTests={async () => {
                        // Mock test execution for now
                        return (formData.testCases || []).map((tc: any) => ({
                          testId: tc.id,
                          success: Math.random() > 0.2,
                          output: { result: 'Mock test result', data: tc.input },
                          executionTime: Math.floor(Math.random() * 100) + 10
                        }));
                      }}
                    />
                  </TabsContent>

                  <TabsContent value="environment" className="mt-4">
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-medium mb-3">Environment Variables</h4>
                        <div className="space-y-2">
                          {Object.entries(formData.environment?.variables || {}).map(([key, value], index) => (
                            <div key={index} className="flex gap-2">
                              <Input
                                placeholder="Variable name"
                                value={key}
                                onChange={(e) => {
                                  const newVars = { ...formData.environment?.variables };
                                  delete newVars[key];
                                  newVars[e.target.value] = value as string;
                                  handleChange('environment', {
                                    ...formData.environment,
                                    variables: newVars
                                  });
                                }}
                              />
                              <Input
                                placeholder="Variable value"
                                value={value as string}
                                onChange={(e) => {
                                  handleChange('environment', {
                                    ...formData.environment,
                                    variables: {
                                      ...formData.environment?.variables,
                                      [key]: e.target.value
                                    }
                                  });
                                }}
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const newVars = { ...formData.environment?.variables };
                                  delete newVars[key];
                                  handleChange('environment', {
                                    ...formData.environment,
                                    variables: newVars
                                  });
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              handleChange('environment', {
                                ...formData.environment,
                                variables: {
                                  ...formData.environment?.variables,
                                  [`VAR_${Date.now()}`]: ''
                                }
                              });
                            }}
                          >
                            <Plus className="w-4 h-4 mr-1" />
                            Add Variable
                          </Button>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Required Secrets</h4>
                        <div className="space-y-2">
                          {(formData.environment?.secrets || []).map((secret: string, index: number) => (
                            <div key={index} className="flex gap-2">
                              <Input
                                placeholder="Secret key name"
                                value={secret}
                                onChange={(e) => {
                                  const newSecrets = [...(formData.environment?.secrets || [])];
                                  newSecrets[index] = e.target.value;
                                  handleChange('environment', {
                                    ...formData.environment,
                                    secrets: newSecrets
                                  });
                                }}
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const newSecrets = (formData.environment?.secrets || []).filter((_: string, i: number) => i !== index);
                                  handleChange('environment', {
                                    ...formData.environment,
                                    secrets: newSecrets
                                  });
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              handleChange('environment', {
                                ...formData.environment,
                                secrets: [...(formData.environment?.secrets || []), '']
                              });
                            }}
                          >
                            <Plus className="w-4 h-4 mr-1" />
                            Add Secret
                          </Button>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Allowed Domains (for HTTP requests)</h4>
                        <div className="space-y-2">
                          {(formData.environment?.allowedDomains || []).map((domain: string, index: number) => (
                            <div key={index} className="flex gap-2">
                              <Input
                                placeholder="example.com"
                                value={domain}
                                onChange={(e) => {
                                  const newDomains = [...(formData.environment?.allowedDomains || [])];
                                  newDomains[index] = e.target.value;
                                  handleChange('environment', {
                                    ...formData.environment,
                                    allowedDomains: newDomains
                                  });
                                }}
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const newDomains = (formData.environment?.allowedDomains || []).filter((_: string, i: number) => i !== index);
                                  handleChange('environment', {
                                    ...formData.environment,
                                    allowedDomains: newDomains
                                  });
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              handleChange('environment', {
                                ...formData.environment,
                                allowedDomains: [...(formData.environment?.allowedDomains || []), '']
                              });
                            }}
                          >
                            <Plus className="w-4 h-4 mr-1" />
                            Add Domain
                          </Button>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            )}

            {formData.credentialId && (
              <div className="col-span-2 pt-2">
                <div className="flex items-center text-neutral-600 dark:text-neutral-400 text-sm">
                  <Key className="w-4 h-4 mr-2 text-primary" />
                  <span>Using credential: <strong>
                    {credentials?.find(c => c.id === parseInt(formData.credentialId))?.name || 'Unknown'}
                  </strong></span>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NodeConfigModal;
